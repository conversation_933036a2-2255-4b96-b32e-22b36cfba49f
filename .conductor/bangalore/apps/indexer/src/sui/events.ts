import type { SuiEventFilter } from '@mysten/sui/client';

import { env, getNetworkConfig } from '@/config/environment';

// Event type definitions based on the smart contract
export interface HopfunEventTypes {
  ConnectorCreated: {
    connector_id: string;
    coin: {
      name: string;
    } | string; // Support both object and string formats
  };

  BondingCurveCreated: {
    curve_id: string;
    creator: string;
    coin_name: string;
    ticker: string;
    description: string;
    image_url?: string;
    twitter: string;
    website: string;
    telegram: string;
    total_supply: string;
  };

  BondingCurveBuy: {
    curve_id: string;
    sui_amount: string;
    token_amount: string;
    pre_price: string;
    post_price: string;
    sender: string;
    is_dev_buy: boolean;
    virtual_sui_amount: string;
    post_sui_balance: string;
    post_token_balance: string;
    available_token_reserves: string;
  };

  BondingCurveSell: {
    curve_id: string;
    sui_amount: string;
    token_amount: string;
    pre_price: string;
    post_price: string;
    virtual_sui_amount: string;
    post_sui_balance: string;
    post_token_balance: string;
    available_token_reserves: string;
  };

  BondingCurveComplete: {
    curve_id: string;
  };

  BondingCurveMigrate: {
    curve_id: string;
    to_pool_id: string;
  };

  MemeConfigUpdated: {
    minimum_version: string;
    is_create_enabled: boolean;
    are_swaps_enabled: boolean;
    virtual_sui_amount: string;
    curve_supply_bps: string;
    listing_fee: string;
    swap_fee_bps: string;
    migration_fee_bps: string;
    treasury: string;
  };
}

export type HopfunEventType = keyof HopfunEventTypes;

// Create event filters for the hopfun package
export class HopfunEventFilters {
  private packageId: string;

  constructor(packageId?: string) {
    const config = getNetworkConfig(env.NETWORK);
    this.packageId = packageId ?? config.packagesIds.hopfun;
  }

  // Generic filter for any event from the package
  getAllEvents(): SuiEventFilter {
    // Return a filter that matches any event from the package's events module
    // Since Sui doesn't support wildcards, we'll use MoveModule filter
    return {
      MoveModule: {
        package: this.packageId,
        module: 'events',
      },
    };
  }

  // Specific event type filters
  getConnectorCreatedFilter(): SuiEventFilter {
    return {
      MoveEventType: `${this.packageId}::events::ConnectorCreated`,
    };
  }

  getBondingCurveCreatedFilter(): SuiEventFilter {
    return {
      MoveEventType: `${this.packageId}::events::BondingCurveCreated`,
    };
  }

  getBondingCurveBuyFilter(): SuiEventFilter {
    return {
      MoveEventType: `${this.packageId}::events::BondingCurveBuy`,
    };
  }

  getBondingCurveSellFilter(): SuiEventFilter {
    return {
      MoveEventType: `${this.packageId}::events::BondingCurveSell`,
    };
  }

  getBondingCurveCompleteFilter(): SuiEventFilter {
    return {
      MoveEventType: `${this.packageId}::events::BondingCurveComplete`,
    };
  }

  getBondingCurveMigrateFilter(): SuiEventFilter {
    return {
      MoveEventType: `${this.packageId}::events::BondingCurveMigrate`,
    };
  }

  getMemeConfigUpdatedFilter(): SuiEventFilter {
    return {
      MoveEventType: `${this.packageId}::events::MemeConfigUpdated`,
    };
  }

  // Filter by specific curve ID
  getCurveSpecificFilter(_curveId: string): SuiEventFilter {
    return {
      MoveEventType: `${this.packageId}::events::BondingCurveBuy`,
      // Note: This would need to be combined with filtering in the event handler
      // as Sui doesn't support field-level filtering in the event filter itself
    };
  }

  // Filter by sender address
  getSenderSpecificFilter(_sender: string): SuiEventFilter {
    return {
      MoveEventType: `${this.packageId}::events::BondingCurveBuy`,
      // Note: Sender filtering would need to be done in post-processing
    };
  }

  // Get all trading events (buy + sell)
  getTradingEventsFilter(): SuiEventFilter[] {
    return [this.getBondingCurveBuyFilter(), this.getBondingCurveSellFilter()];
  }

  // Get all lifecycle events
  getLifecycleEventsFilter(): SuiEventFilter[] {
    return [
      this.getBondingCurveCreatedFilter(),
      this.getBondingCurveCompleteFilter(),
      this.getBondingCurveMigrateFilter(),
    ];
  }
}

// Utility functions for event validation and parsing
export class EventValidator {
  static isValidConnectorCreatedEvent(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any,
  ): data is HopfunEventTypes['ConnectorCreated'] {
    return (
      typeof data === 'object' &&
      typeof data.connector_id === 'string' &&
      (typeof data.coin === 'string' || 
       (typeof data.coin === 'object' && 
        data.coin !== null && 
        typeof data.coin.name === 'string'))
    );
  }

  static isValidBondingCurveCreatedEvent(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any,
  ): data is HopfunEventTypes['BondingCurveCreated'] {
    return (
      typeof data === 'object' &&
      typeof data.curve_id === 'string' &&
      typeof data.creator === 'string' &&
      typeof data.coin_name === 'string' &&
      typeof data.ticker === 'string' &&
      typeof data.description === 'string' &&
      typeof data.twitter === 'string' &&
      typeof data.website === 'string' &&
      typeof data.telegram === 'string' &&
      typeof data.total_supply === 'string'
    );
  }

  static isValidBondingCurveBuyEvent(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any,
  ): data is HopfunEventTypes['BondingCurveBuy'] {
    return (
      typeof data === 'object' &&
      typeof data.curve_id === 'string' &&
      typeof data.sui_amount === 'string' &&
      typeof data.token_amount === 'string' &&
      typeof data.pre_price === 'string' &&
      typeof data.post_price === 'string' &&
      typeof data.sender === 'string' &&
      typeof data.is_dev_buy === 'boolean' &&
      typeof data.virtual_sui_amount === 'string' &&
      typeof data.post_sui_balance === 'string' &&
      typeof data.post_token_balance === 'string' &&
      typeof data.available_token_reserves === 'string'
    );
  }

  static isValidBondingCurveSellEvent(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any,
  ): data is HopfunEventTypes['BondingCurveSell'] {
    return (
      typeof data === 'object' &&
      typeof data.curve_id === 'string' &&
      typeof data.sui_amount === 'string' &&
      typeof data.token_amount === 'string' &&
      typeof data.pre_price === 'string' &&
      typeof data.post_price === 'string' &&
      typeof data.virtual_sui_amount === 'string' &&
      typeof data.post_sui_balance === 'string' &&
      typeof data.post_token_balance === 'string' &&
      typeof data.available_token_reserves === 'string'
    );
  }

  static isValidBondingCurveCompleteEvent(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any,
  ): data is HopfunEventTypes['BondingCurveComplete'] {
    return typeof data === 'object' && typeof data.curve_id === 'string';
  }

  static isValidBondingCurveMigrateEvent(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any,
  ): data is HopfunEventTypes['BondingCurveMigrate'] {
    return (
      typeof data === 'object' &&
      typeof data.curve_id === 'string' &&
      typeof data.to_pool_id === 'string'
    );
  }

  static isValidMemeConfigUpdatedEvent(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any,
  ): data is HopfunEventTypes['MemeConfigUpdated'] {
    return (
      typeof data === 'object' &&
      typeof data.minimum_version === 'string' &&
      typeof data.is_create_enabled === 'boolean' &&
      typeof data.are_swaps_enabled === 'boolean' &&
      typeof data.virtual_sui_amount === 'string' &&
      typeof data.curve_supply_bps === 'string' &&
      typeof data.listing_fee === 'string' &&
      typeof data.swap_fee_bps === 'string' &&
      typeof data.migration_fee_bps === 'string' &&
      typeof data.treasury === 'string'
    );
  }
}

// Event type detection utility
export function getEventType(eventType: string): HopfunEventType | null {
  const eventTypes: Record<string, HopfunEventType> = {
    ConnectorCreated: 'ConnectorCreated',
    BondingCurveCreated: 'BondingCurveCreated',
    BondingCurveBuy: 'BondingCurveBuy',
    BondingCurveSell: 'BondingCurveSell',
    BondingCurveComplete: 'BondingCurveComplete',
    BondingCurveMigrate: 'BondingCurveMigrate',
    MemeConfigUpdated: 'MemeConfigUpdated',
  };

  // Handle generic type parameters (e.g., BondingCurveCreated<0x...::template::TEMPLATE>)
  // Remove the generic type parameter if present
  const cleanEventType = eventType.split('<')[0];
  
  // Extract event name from full event type
  const eventName = cleanEventType.split('::').pop();

  if (eventName && eventTypes.hasOwnProperty(eventName)) {
    return eventTypes[eventName];
  }
  return null;
}

export default HopfunEventFilters;
